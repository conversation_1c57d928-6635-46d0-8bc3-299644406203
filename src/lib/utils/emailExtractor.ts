/**
 * Email Extraction Utility
 * Extracts email addresses and contact information from email threads
 */

import type { EmailThread, EmailMessage } from '$lib/components/conversation/EmailThread.svelte';
import { contactService, type EmailContact } from '$lib/services/contactService';

export interface ExtractedContact {
	email: string;
	name?: string;
	source: 'sender' | 'participant' | 'recipient';
}

export interface ThreadContactInfo {
	threadId: string;
	contacts: ExtractedContact[];
	totalContacts: number;
	newContacts: number;
}

/**
 * Extract email addresses from a single email message
 */
export function extractContactsFromMessage(message: EmailMessage): ExtractedContact[] {
	const contacts: ExtractedContact[] = [];
	const seenEmails = new Set<string>();

	// Extract sender information
	if (message.sender.email && !seenEmails.has(message.sender.email.toLowerCase())) {
		contacts.push({
			email: message.sender.email,
			name: message.sender.name,
			source: 'sender'
		});
		seenEmails.add(message.sender.email.toLowerCase());
	}

	return contacts;
}

/**
 * Extract all contacts from an email thread
 */
export function extractContactsFromThread(thread: EmailThread): ExtractedContact[] {
	const contacts: ExtractedContact[] = [];
	const seenEmails = new Set<string>();

	// Extract from thread participants
	thread.participants.forEach(participant => {
		// Parse participant string - could be "Name <email>" or just "email"
		const parsed = parseParticipantString(participant);
		if (parsed && !seenEmails.has(parsed.email.toLowerCase())) {
			contacts.push({
				email: parsed.email,
				name: parsed.name,
				source: 'participant'
			});
			seenEmails.add(parsed.email.toLowerCase());
		}
	});

	// Extract from individual messages
	thread.messages.forEach(message => {
		const messageContacts = extractContactsFromMessage(message);
		messageContacts.forEach(contact => {
			if (!seenEmails.has(contact.email.toLowerCase())) {
				contacts.push(contact);
				seenEmails.add(contact.email.toLowerCase());
			}
		});
	});

	return contacts;
}

/**
 * Extract contacts from multiple threads
 */
export function extractContactsFromThreads(threads: EmailThread[]): ExtractedContact[] {
	const allContacts: ExtractedContact[] = [];
	const seenEmails = new Set<string>();

	threads.forEach(thread => {
		const threadContacts = extractContactsFromThread(thread);
		threadContacts.forEach(contact => {
			if (!seenEmails.has(contact.email.toLowerCase())) {
				allContacts.push(contact);
				seenEmails.add(contact.email.toLowerCase());
			}
		});
	});

	return allContacts;
}

/**
 * Process and save contacts from threads to the contact service
 */
export function processAndSaveThreadContacts(threads: EmailThread[]): ThreadContactInfo[] {
	const results: ThreadContactInfo[] = [];

	threads.forEach(thread => {
		const extractedContacts = extractContactsFromThread(thread);
		let newContactsCount = 0;

		// Add contacts to the service
		const savedContacts: EmailContact[] = [];
		extractedContacts.forEach(extracted => {
			try {
				const existingContact = contactService.getContact(extracted.email);
				const savedContact = contactService.addContact(extracted.email, extracted.name, 'thread');
				
				if (!existingContact) {
					newContactsCount++;
				}
				
				savedContacts.push(savedContact);
			} catch (error) {
				console.warn(`Failed to save contact ${extracted.email}:`, error);
			}
		});

		results.push({
			threadId: thread.id,
			contacts: extractedContacts,
			totalContacts: extractedContacts.length,
			newContacts: newContactsCount
		});
	});

	return results;
}

/**
 * Parse participant string to extract email and name
 * Handles formats like:
 * - "<EMAIL>"
 * - "John Doe <<EMAIL>>"
 * - "John Doe"
 */
export function parseParticipantString(participant: string): {email: string, name?: string} | null {
	if (!participant || typeof participant !== 'string') {
		return null;
	}

	const trimmed = participant.trim();
	
	// Check for "Name <email>" format
	const nameEmailMatch = trimmed.match(/^(.+?)\s*<([^>]+)>$/);
	if (nameEmailMatch) {
		const name = nameEmailMatch[1].trim();
		const email = nameEmailMatch[2].trim();
		if (isValidEmail(email)) {
			return { email, name: name || undefined };
		}
	}

	// Check if it's just an email
	if (isValidEmail(trimmed)) {
		return { email: trimmed };
	}

	// Check if it's a name that might contain an email
	const emailInNameMatch = trimmed.match(/([^\s@]+@[^\s@]+\.[^\s@]+)/);
	if (emailInNameMatch) {
		const email = emailInNameMatch[1];
		if (isValidEmail(email)) {
			const name = trimmed.replace(email, '').trim().replace(/[<>]/g, '').trim();
			return { email, name: name || undefined };
		}
	}

	return null;
}

/**
 * Extract email addresses from a comma-separated string
 * Used for parsing To, CC, BCC fields
 */
export function parseEmailList(emailString: string): Array<{email: string, name?: string}> {
	if (!emailString || typeof emailString !== 'string') {
		return [];
	}

	const emails: Array<{email: string, name?: string}> = [];
	const parts = emailString.split(',');

	parts.forEach(part => {
		const parsed = parseParticipantString(part);
		if (parsed) {
			emails.push(parsed);
		}
	});

	return emails;
}

/**
 * Format contact for display in recipient fields
 */
export function formatContactForDisplay(contact: EmailContact): string {
	if (contact.name && contact.name !== contact.email) {
		return `${contact.name} <${contact.email}>`;
	}
	return contact.email;
}

/**
 * Format multiple contacts for display
 */
export function formatContactsForDisplay(contacts: EmailContact[]): string {
	return contacts.map(formatContactForDisplay).join(', ');
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
	if (!email || typeof email !== 'string') {
		return false;
	}
	
	const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
	return emailRegex.test(email.trim());
}

/**
 * Extract unique domains from contacts
 */
export function extractDomains(contacts: ExtractedContact[]): string[] {
	const domains = new Set<string>();
	
	contacts.forEach(contact => {
		const domain = contact.email.split('@')[1];
		if (domain) {
			domains.add(domain.toLowerCase());
		}
	});

	return Array.from(domains).sort();
}

/**
 * Get contact statistics from extracted contacts
 */
export function getContactStats(contacts: ExtractedContact[]): {
	totalContacts: number;
	senders: number;
	participants: number;
	recipients: number;
	domains: number;
} {
	const stats = {
		totalContacts: contacts.length,
		senders: 0,
		participants: 0,
		recipients: 0,
		domains: 0
	};

	const domains = new Set<string>();

	contacts.forEach(contact => {
		switch (contact.source) {
			case 'sender':
				stats.senders++;
				break;
			case 'participant':
				stats.participants++;
				break;
			case 'recipient':
				stats.recipients++;
				break;
		}

		const domain = contact.email.split('@')[1];
		if (domain) {
			domains.add(domain.toLowerCase());
		}
	});

	stats.domains = domains.size;
	return stats;
}

/**
 * Deduplicate contacts by email address
 */
export function deduplicateContacts(contacts: ExtractedContact[]): ExtractedContact[] {
	const seen = new Map<string, ExtractedContact>();

	contacts.forEach(contact => {
		const key = contact.email.toLowerCase();
		const existing = seen.get(key);

		if (!existing) {
			seen.set(key, contact);
		} else {
			// Keep the contact with more information (name preferred)
			if (contact.name && !existing.name) {
				seen.set(key, contact);
			}
		}
	});

	return Array.from(seen.values());
}

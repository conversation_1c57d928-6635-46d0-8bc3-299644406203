<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy, tick } from 'svelte';
	import { contactService, type EmailContact, type ContactSearchResult } from '$lib/services/contactService';
	import { parseEmailList, formatContactForDisplay, isValidEmail } from '$lib/utils/emailExtractor';
	import { UserOutline, CloseOutline } from 'flowbite-svelte-icons';

	// Props
	export let value: string = '';
	export let placeholder: string = 'Enter email addresses...';
	export let disabled: boolean = false;
	export let multiple: boolean = true;
	export let maxSuggestions: number = 8;
	export let minQueryLength: number = 1;
	export let id: string = '';
	export let name: string = '';

	// Component state
	let inputElement: HTMLInputElement;
	let dropdownElement: HTMLDivElement;
	let currentQuery: string = '';
	let suggestions: ContactSearchResult[] = [];
	let selectedIndex: number = -1;
	let showDropdown: boolean = false;
	let selectedContacts: EmailContact[] = [];
	let inputValue: string = '';

	const dispatch = createEventDispatcher<{
		change: { value: string; contacts: EmailContact[] };
		input: { value: string };
		focus: void;
		blur: void;
	}>();

	// Initialize component
	onMount(() => {
		parseInitialValue();
		document.addEventListener('click', handleOutsideClick);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleOutsideClick);
	});

	// Parse initial value into selected contacts
	function parseInitialValue() {
		if (value) {
			const parsed = parseEmailList(value);
			selectedContacts = parsed.map(p => {
				const existing = contactService.getContact(p.email);
				return existing || {
					id: `temp_${Date.now()}_${Math.random()}`,
					email: p.email,
					name: p.name,
					displayName: p.name || p.email,
					frequency: 0,
					lastSeen: new Date(),
					source: 'manual' as const
				};
			});
		}
	}

	// Handle input changes
	function handleInput(event: Event) {
		const target = event.target as HTMLInputElement;
		inputValue = target.value;
		currentQuery = inputValue.trim();

		dispatch('input', { value: inputValue });

		if (currentQuery.length >= minQueryLength) {
			searchContacts();
		} else {
			hideSuggestions();
		}
	}

	// Search for contacts
	async function searchContacts() {
		if (!currentQuery) {
			hideSuggestions();
			return;
		}

		suggestions = contactService.searchContacts(currentQuery, maxSuggestions);
		
		// Filter out already selected contacts
		const selectedEmails = new Set(selectedContacts.map(c => c.email.toLowerCase()));
		suggestions = suggestions.filter(s => !selectedEmails.has(s.contact.email.toLowerCase()));

		selectedIndex = -1;
		showDropdown = suggestions.length > 0;

		if (showDropdown) {
			await tick();
			positionDropdown();
		}
	}

	// Position dropdown relative to input
	function positionDropdown() {
		if (!dropdownElement || !inputElement) return;

		const inputRect = inputElement.getBoundingClientRect();
		const dropdownRect = dropdownElement.getBoundingClientRect();
		const viewportHeight = window.innerHeight;

		// Check if there's enough space below
		const spaceBelow = viewportHeight - inputRect.bottom;
		const spaceAbove = inputRect.top;

		if (spaceBelow >= dropdownRect.height || spaceBelow >= spaceAbove) {
			// Show below
			dropdownElement.style.top = `${inputRect.bottom + window.scrollY}px`;
		} else {
			// Show above
			dropdownElement.style.top = `${inputRect.top + window.scrollY - dropdownRect.height}px`;
		}

		dropdownElement.style.left = `${inputRect.left + window.scrollX}px`;
		dropdownElement.style.width = `${inputRect.width}px`;
	}

	// Hide suggestions
	function hideSuggestions() {
		showDropdown = false;
		selectedIndex = -1;
	}

	// Handle keyboard navigation
	function handleKeyDown(event: KeyboardEvent) {
		if (!showDropdown) {
			if (event.key === 'ArrowDown') {
				event.preventDefault();
				searchContacts();
			}
			return;
		}

		switch (event.key) {
			case 'ArrowDown':
				event.preventDefault();
				selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
				break;
			case 'ArrowUp':
				event.preventDefault();
				selectedIndex = Math.max(selectedIndex - 1, -1);
				break;
			case 'Enter':
				event.preventDefault();
				if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
					selectContact(suggestions[selectedIndex].contact);
				} else if (currentQuery && isValidEmail(currentQuery)) {
					addManualContact(currentQuery);
				}
				break;
			case 'Escape':
				event.preventDefault();
				hideSuggestions();
				break;
			case 'Tab':
				if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
					event.preventDefault();
					selectContact(suggestions[selectedIndex].contact);
				}
				break;
		}
	}

	// Select a contact from suggestions
	function selectContact(contact: EmailContact) {
		if (multiple) {
			selectedContacts = [...selectedContacts, contact];
		} else {
			selectedContacts = [contact];
		}
		
		inputValue = '';
		currentQuery = '';
		hideSuggestions();
		updateValue();
		
		// Focus back to input for multiple selection
		if (multiple) {
			inputElement?.focus();
		}
	}

	// Add manual contact (typed email)
	function addManualContact(email: string) {
		if (!isValidEmail(email)) return;

		const contact: EmailContact = {
			id: `manual_${Date.now()}_${Math.random()}`,
			email: email.toLowerCase().trim(),
			displayName: email,
			frequency: 0,
			lastSeen: new Date(),
			source: 'manual'
		};

		// Add to contact service
		contactService.addContact(contact.email, undefined, 'manual');

		if (multiple) {
			selectedContacts = [...selectedContacts, contact];
		} else {
			selectedContacts = [contact];
		}

		inputValue = '';
		currentQuery = '';
		hideSuggestions();
		updateValue();

		if (multiple) {
			inputElement?.focus();
		}
	}

	// Remove selected contact
	function removeContact(index: number) {
		selectedContacts = selectedContacts.filter((_, i) => i !== index);
		updateValue();
		inputElement?.focus();
	}

	// Update the component value
	function updateValue() {
		const newValue = selectedContacts.map(formatContactForDisplay).join(', ');
		value = newValue;
		dispatch('change', { value: newValue, contacts: selectedContacts });
	}

	// Handle outside clicks
	function handleOutsideClick(event: MouseEvent) {
		const target = event.target as Element;
		if (!inputElement?.contains(target) && !dropdownElement?.contains(target)) {
			hideSuggestions();
		}
	}

	// Handle focus
	function handleFocus() {
		dispatch('focus');
		if (currentQuery.length >= minQueryLength) {
			searchContacts();
		}
	}

	// Handle blur
	function handleBlur() {
		// Delay to allow for dropdown clicks
		setTimeout(() => {
			dispatch('blur');
		}, 150);
	}

	// Get contact initials for avatar
	function getContactInitials(contact: EmailContact): string {
		if (contact.name) {
			return contact.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
		}
		return contact.email.slice(0, 2).toUpperCase();
	}

	// Get avatar color
	function getAvatarColor(contact: EmailContact): string {
		const colors = [
			'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
			'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
		];
		
		let hash = 0;
		const str = contact.email;
		for (let i = 0; i < str.length; i++) {
			hash = str.charCodeAt(i) + ((hash << 5) - hash);
		}
		
		return colors[Math.abs(hash) % colors.length];
	}
</script>

<div class="relative">
	<!-- Selected contacts (for multiple mode) -->
	{#if multiple && selectedContacts.length > 0}
		<div class="flex flex-wrap gap-1 mb-2">
			{#each selectedContacts as contact, index (contact.id)}
				<span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
					<div class="w-4 h-4 rounded-full {getAvatarColor(contact)} flex items-center justify-center text-white text-xs mr-1">
						{getContactInitials(contact)}
					</div>
					{contact.displayName}
					<button
						type="button"
						class="ml-1 inline-flex items-center justify-center w-4 h-4 rounded-full hover:bg-blue-200 focus:outline-none"
						on:click={() => removeContact(index)}
					>
						<CloseOutline class="w-3 h-3" />
					</button>
				</span>
			{/each}
		</div>
	{/if}

	<!-- Input field -->
	<div class="relative">
		<input
			bind:this={inputElement}
			bind:value={inputValue}
			on:input={handleInput}
			on:keydown={handleKeyDown}
			on:focus={handleFocus}
			on:blur={handleBlur}
			{id}
			{name}
			{placeholder}
			{disabled}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent {disabled ? 'bg-gray-100 cursor-not-allowed' : ''}"
			autocomplete="off"
			spellcheck="false"
		/>
		
		{#if !multiple && selectedContacts.length > 0}
			<button
				type="button"
				class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
				on:click={() => { selectedContacts = []; updateValue(); }}
			>
				<CloseOutline class="w-4 h-4" />
			</button>
		{/if}
	</div>

	<!-- Dropdown suggestions -->
	{#if showDropdown}
		<div
			bind:this={dropdownElement}
			class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
		>
			{#each suggestions as suggestion, index (suggestion.contact.id)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none {selectedIndex === index ? 'bg-blue-50' : ''}"
					on:click={() => selectContact(suggestion.contact)}
				>
					<div class="flex items-center space-x-2">
						<div class="w-6 h-6 rounded-full {getAvatarColor(suggestion.contact)} flex items-center justify-center text-white text-xs">
							{getContactInitials(suggestion.contact)}
						</div>
						<div class="flex-1 min-w-0">
							<div class="text-sm font-medium text-gray-900 truncate">
								{suggestion.contact.displayName}
							</div>
							{#if suggestion.contact.name && suggestion.contact.name !== suggestion.contact.email}
								<div class="text-xs text-gray-500 truncate">
									{suggestion.contact.email}
								</div>
							{/if}
						</div>
						{#if suggestion.contact.frequency > 1}
							<div class="text-xs text-gray-400">
								{suggestion.contact.frequency}x
							</div>
						{/if}
					</div>
				</button>
			{/each}
			
			{#if suggestions.length === 0 && currentQuery && isValidEmail(currentQuery)}
				<button
					type="button"
					class="w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none"
					on:click={() => addManualContact(currentQuery)}
				>
					<div class="flex items-center space-x-2">
						<UserOutline class="w-6 h-6 text-gray-400" />
						<div class="flex-1">
							<div class="text-sm text-gray-900">
								Add "{currentQuery}"
							</div>
							<div class="text-xs text-gray-500">
								Press Enter to add this email
							</div>
						</div>
					</div>
				</button>
			{/if}
		</div>
	{/if}
</div>

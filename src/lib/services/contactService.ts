/**
 * Contact Management Service
 * Handles storage, retrieval, and management of email contacts for auto-completion
 */

export interface EmailContact {
	id: string;
	email: string;
	name?: string;
	displayName: string; // Either name or email if name is not available
	frequency: number; // How often this contact appears in threads
	lastSeen: Date;
	source: 'thread' | 'manual'; // How the contact was added
	avatar?: string;
}

export interface ContactSearchResult {
	contact: EmailContact;
	score: number; // Relevance score for search results
}

class ContactService {
	private static instance: ContactService;
	private contacts: Map<string, EmailContact> = new Map();
	private readonly STORAGE_KEY = 'email_contacts';
	private readonly MAX_CONTACTS = 1000; // Limit to prevent excessive storage usage

	private constructor() {
		this.loadContacts();
	}

	public static getInstance(): ContactService {
		if (!ContactService.instance) {
			ContactService.instance = new ContactService();
		}
		return ContactService.instance;
	}

	/**
	 * Load contacts from localStorage
	 */
	private loadContacts(): void {
		try {
			const stored = localStorage.getItem(this.STORAGE_KEY);
			if (stored) {
				const contactsData = JSON.parse(stored);
				this.contacts = new Map();
				
				// Convert stored data back to Map with proper Date objects
				Object.entries(contactsData).forEach(([email, data]: [string, any]) => {
					this.contacts.set(email, {
						...data,
						lastSeen: new Date(data.lastSeen)
					});
				});
			}
		} catch (error) {
			console.error('Failed to load contacts from localStorage:', error);
			this.contacts = new Map();
		}
	}

	/**
	 * Save contacts to localStorage
	 */
	private saveContacts(): void {
		try {
			// Convert Map to plain object for JSON serialization
			const contactsObj: Record<string, any> = {};
			this.contacts.forEach((contact, email) => {
				contactsObj[email] = {
					...contact,
					lastSeen: contact.lastSeen.toISOString()
				};
			});
			
			localStorage.setItem(this.STORAGE_KEY, JSON.stringify(contactsObj));
		} catch (error) {
			console.error('Failed to save contacts to localStorage:', error);
		}
	}

	/**
	 * Add or update a contact
	 */
	public addContact(email: string, name?: string, source: 'thread' | 'manual' = 'thread'): EmailContact {
		// Validate email format
		if (!this.isValidEmail(email)) {
			throw new Error(`Invalid email format: ${email}`);
		}

		const normalizedEmail = email.toLowerCase().trim();
		const existing = this.contacts.get(normalizedEmail);
		
		if (existing) {
			// Update existing contact
			existing.frequency += 1;
			existing.lastSeen = new Date();
			if (name && !existing.name) {
				existing.name = name;
				existing.displayName = name;
			}
		} else {
			// Create new contact
			const contact: EmailContact = {
				id: this.generateContactId(),
				email: normalizedEmail,
				name: name,
				displayName: name || normalizedEmail,
				frequency: 1,
				lastSeen: new Date(),
				source
			};
			
			this.contacts.set(normalizedEmail, contact);
			
			// Enforce contact limit
			this.enforceContactLimit();
		}

		this.saveContacts();
		return this.contacts.get(normalizedEmail)!;
	}

	/**
	 * Add multiple contacts from thread participants
	 */
	public addContactsFromThread(participants: Array<{email: string, name?: string}>): EmailContact[] {
		const addedContacts: EmailContact[] = [];
		
		participants.forEach(participant => {
			try {
				const contact = this.addContact(participant.email, participant.name, 'thread');
				addedContacts.push(contact);
			} catch (error) {
				console.warn(`Failed to add contact ${participant.email}:`, error);
			}
		});

		return addedContacts;
	}

	/**
	 * Search contacts by query string
	 */
	public searchContacts(query: string, limit: number = 10): ContactSearchResult[] {
		if (!query.trim()) {
			return [];
		}

		const normalizedQuery = query.toLowerCase().trim();
		const results: ContactSearchResult[] = [];

		this.contacts.forEach(contact => {
			const score = this.calculateSearchScore(contact, normalizedQuery);
			if (score > 0) {
				results.push({ contact, score });
			}
		});

		// Sort by score (descending) and frequency (descending)
		results.sort((a, b) => {
			if (a.score !== b.score) {
				return b.score - a.score;
			}
			return b.contact.frequency - a.contact.frequency;
		});

		return results.slice(0, limit);
	}

	/**
	 * Get all contacts sorted by frequency and recency
	 */
	public getAllContacts(): EmailContact[] {
		const contacts = Array.from(this.contacts.values());
		
		// Sort by frequency (descending) and lastSeen (descending)
		contacts.sort((a, b) => {
			if (a.frequency !== b.frequency) {
				return b.frequency - a.frequency;
			}
			return b.lastSeen.getTime() - a.lastSeen.getTime();
		});

		return contacts;
	}

	/**
	 * Get contact by email
	 */
	public getContact(email: string): EmailContact | undefined {
		return this.contacts.get(email.toLowerCase().trim());
	}

	/**
	 * Remove a contact
	 */
	public removeContact(email: string): boolean {
		const normalizedEmail = email.toLowerCase().trim();
		const deleted = this.contacts.delete(normalizedEmail);
		if (deleted) {
			this.saveContacts();
		}
		return deleted;
	}

	/**
	 * Clear all contacts
	 */
	public clearAllContacts(): void {
		this.contacts.clear();
		this.saveContacts();
	}

	/**
	 * Get contact statistics
	 */
	public getStats(): {totalContacts: number, threadContacts: number, manualContacts: number} {
		const contacts = Array.from(this.contacts.values());
		return {
			totalContacts: contacts.length,
			threadContacts: contacts.filter(c => c.source === 'thread').length,
			manualContacts: contacts.filter(c => c.source === 'manual').length
		};
	}

	/**
	 * Calculate search relevance score
	 */
	private calculateSearchScore(contact: EmailContact, query: string): number {
		let score = 0;

		// Exact email match gets highest score
		if (contact.email === query) {
			score += 100;
		}
		// Email starts with query
		else if (contact.email.startsWith(query)) {
			score += 80;
		}
		// Email contains query
		else if (contact.email.includes(query)) {
			score += 60;
		}

		// Name matching (if available)
		if (contact.name) {
			const normalizedName = contact.name.toLowerCase();
			if (normalizedName === query) {
				score += 90;
			} else if (normalizedName.startsWith(query)) {
				score += 70;
			} else if (normalizedName.includes(query)) {
				score += 50;
			}
		}

		// Boost score based on frequency (up to 20 points)
		score += Math.min(contact.frequency * 2, 20);

		// Boost score for recent contacts (up to 10 points)
		const daysSinceLastSeen = (Date.now() - contact.lastSeen.getTime()) / (1000 * 60 * 60 * 24);
		if (daysSinceLastSeen < 7) {
			score += 10 - Math.floor(daysSinceLastSeen);
		}

		return score;
	}

	/**
	 * Validate email format
	 */
	private isValidEmail(email: string): boolean {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		return emailRegex.test(email);
	}

	/**
	 * Generate unique contact ID
	 */
	private generateContactId(): string {
		return `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
	}

	/**
	 * Enforce contact limit by removing least used contacts
	 */
	private enforceContactLimit(): void {
		if (this.contacts.size <= this.MAX_CONTACTS) {
			return;
		}

		const contacts = Array.from(this.contacts.entries());
		
		// Sort by frequency (ascending) and lastSeen (ascending) to remove least valuable contacts
		contacts.sort(([, a], [, b]) => {
			if (a.frequency !== b.frequency) {
				return a.frequency - b.frequency;
			}
			return a.lastSeen.getTime() - b.lastSeen.getTime();
		});

		// Remove excess contacts
		const toRemove = contacts.slice(0, this.contacts.size - this.MAX_CONTACTS);
		toRemove.forEach(([email]) => {
			this.contacts.delete(email);
		});
	}
}

// Export singleton instance
export const contactService = ContactService.getInstance();
